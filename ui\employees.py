from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QTabWidget,
                            QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
                            QMenu, QAction)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QPainter
import re

from database import Employee
from utils import format_currency
from ui.unified_styles import UnifiedStyles

class EmployeesWidget(QWidget):
    """واجهة إدارة العمال"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.current_filter_value = None
            self.current_wages_filter_value = None
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم العمال: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم العمال: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(2)

        # إنشاء التبويبات مع تصميم مطابق للإشعارات
        self.tabs = QTabWidget()
        # تعيين موضع التبويبات في الأعلى (الافتراضي)
        self.tabs.setTabPosition(QTabWidget.North)


        # تحسين تصميم التبويبات مع إطار أسود وعرض أكبر مطابق للإشعارات
        self.tabs.setStyleSheet("""
            QTabWidget {
                border: 3px solid #000000;
                border-radius: 8px;
                background-color: #f8fafc;
                font-size: 14px;
                font-weight: bold;
            }
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background-color: #ffffff;
                top: -3px;
            }
            QTabBar {
                alignment: center;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                min-width: 878px;
                max-width: 878px;
                min-height: 30px;
                max-height: 30px;
                padding: 8px 32px;
                margin: 2px;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                font-weight: bold;
                font-size: 20px;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                border-radius: 12px;
                margin-top: -1px;
                padding: 9px 32px;
                min-width: 878px;
                max-width: 878px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                border: 4px solid #3B82F6;
                color: #ffffff;
                font-weight: 800;
                font-size: 20px;
                min-width: 878px;
                max-width: 878px;
                min-height: 30px;
                padding: 8px 32px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3),
                           0 2px 8px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إنشاء التبويبات
        self.create_employees_tab()
        self.create_daily_wages_tab()



        # إضافة العناصر للتخطيط بالترتيب الصحيح
        main_layout.addWidget(self.tabs)  # التبويبات فقط
        self.setLayout(main_layout)

    def create_employees_tab(self):
        """إنشاء تبويب إدارة حسابات وبيانات العمال"""
        employees_widget = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة عنوان قسم إدارة العمال
        employees_title = QLabel("👷‍♂️ إدارة بيانات وحسابات العمال - نظام شامل ومتقدم لإدارة الموظفين والمعلومات الشخصية والوظيفية")
        employees_title.setFont(QFont("Segoe UI", 18, QFont.Bold))
        employees_title.setAlignment(Qt.AlignCenter)
        employees_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        layout.addWidget(employees_title)

        # إنشاء إطار علوي للبحث والتصفية مطابق للفواتير
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث باسم العامل، الهاتف، البريد أو العنوان...")
        self.search_edit.textChanged.connect(self.filter_employees)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_employees)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة للعمال
        self.create_custom_employees_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        layout.addWidget(top_frame)

        # إنشاء جدول العمال
        self.create_employees_table()
        layout.addWidget(self.employees_table)

        # إنشاء إطار سفلي للأزرار مطابق للفواتير
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # إنشاء تخطيط عمودي للإطار السفلي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(0, 0, 0, 0)
        bottom_container.setSpacing(0)

        # إنشاء تخطيط أفقي للأزرار
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(8, 8, 8, 8)
        actions_layout.setSpacing(4)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار (سيتم إضافتها في الخطوة التالية)
        self.create_employees_buttons(actions_layout)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # إضافة الإطار السفلي للتخطيط
        layout.addWidget(bottom_frame)

        employees_widget.setLayout(layout)
        self.tabs.addTab(employees_widget, "👥 إدارة بيانات وحسابات العمال")

        # تهيئة حالة الأزرار (الأزرار الأساسية مفعلة، أزرار التحديد معطلة)
        self.initialize_employees_button_states()

        # تحميل البيانات
        self.create_employees_sample_data_if_empty()
        self.refresh_employees_data()

    def create_daily_wages_tab(self):
        """إنشاء تبويب إدارة الأجور اليومية"""
        wages_widget = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة عنوان قسم الأجور اليومية
        wages_title = QLabel("💰 إدارة الأجور اليومية للعمال - نظام متكامل ومتطور للمحاسبة والتقارير المالية والرواتب")
        wages_title.setFont(QFont("Segoe UI", 18, QFont.Bold))
        wages_title.setAlignment(Qt.AlignCenter)
        wages_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        layout.addWidget(wages_title)

        # إنشاء إطار علوي للبحث والتصفية مطابق للفواتير
        wages_top_frame = QFrame()
        wages_top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        wages_search_layout = QHBoxLayout()
        wages_search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        wages_search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        wages_top_container = QVBoxLayout()
        wages_top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        wages_top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        wages_top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        wages_top_container.addLayout(wages_search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        wages_top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        wages_search_label = QLabel("🔍 بحث:")
        wages_search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        wages_search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.daily_wages_search_edit = QLineEdit()
        self.daily_wages_search_edit.setPlaceholderText("🔎 ابحث في الأجور اليومية، العامل أو الملاحظات...")
        self.daily_wages_search_edit.textChanged.connect(self.filter_daily_wages)
        self.daily_wages_search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        wages_search_button = QPushButton("🔍")
        wages_search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        wages_search_button.clicked.connect(self.filter_daily_wages)
        wages_search_button.setToolTip("بحث متقدم")
        wages_search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        wages_search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية
        wages_filter_label = QLabel("🎯 نوع:")
        wages_filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        wages_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة للأجور اليومية
        self.create_custom_wages_type_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        wages_search_layout.addWidget(wages_search_label, 0, Qt.AlignVCenter)
        wages_search_layout.addWidget(self.daily_wages_search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        wages_search_layout.addWidget(wages_search_button, 0, Qt.AlignVCenter)
        wages_search_layout.addWidget(wages_filter_label, 0, Qt.AlignVCenter)
        wages_search_layout.addWidget(self.wages_type_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        wages_top_frame.setLayout(wages_top_container)

        layout.addWidget(wages_top_frame)

        # رسالة مؤقتة
        message_label = QLabel("💰 سيتم إضافة جدول الأجور اليومية وإدارة الرواتب قريباً")
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        message_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                background: #f8fafc;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }
        """)
        layout.addWidget(message_label)

        wages_widget.setLayout(layout)
        self.tabs.addTab(wages_widget, "💰 إدارة الأجور اليومية للعمال")

    def create_employees_buttons(self, actions_layout):
        """إنشاء أزرار العمال مطابقة للفواتير"""

        # زر الإضافة
        self.add_employee_button = QPushButton("➕ إضافة عامل")
        self.style_advanced_button(self.add_employee_button, 'emerald')
        self.add_employee_button.clicked.connect(self.add_employee)
        self.add_employee_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التعديل
        self.edit_employee_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_employee_button, 'info')
        self.edit_employee_button.clicked.connect(self.edit_employee)
        self.edit_employee_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الحذف
        self.delete_employee_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_employee_button, 'danger')
        self.delete_employee_button.clicked.connect(self.delete_employee)
        self.delete_employee_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث
        self.refresh_employee_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_employee_button, 'modern_teal')
        self.refresh_employee_button.clicked.connect(self.refresh_employees_data)
        self.refresh_employee_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر عرض التفاصيل
        self.view_employee_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_employee_button, 'indigo')
        self.view_employee_button.clicked.connect(self.view_employee_details)
        self.view_employee_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التصدير
        self.export_employee_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_employee_button, 'info', has_menu=True)
        self.export_employee_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير
        export_menu = QMenu(self)
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(248, 250, 252, 0.9),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.8);
                border-radius: 12px;
                padding: 8px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #1f2937;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }
            QMenu::item {
                padding: 8px 20px;
                margin: 2px;
                border-radius: 8px;
                background: transparent;
                color: #374151;
                font-weight: 600;
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        excel_action = QAction("📊 تصدير Excel", self)
        excel_action.triggered.connect(self.export_employees_to_excel)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV", self)
        csv_action.triggered.connect(self.export_employees_to_csv)
        export_menu.addAction(csv_action)

        self.export_employee_button.setMenu(export_menu)

        # زر الإحصائيات
        self.statistics_employee_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_employee_button, 'rose')
        self.statistics_employee_button.clicked.connect(self.show_employees_statistics)
        self.statistics_employee_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_employee_button)
        actions_layout.addWidget(self.edit_employee_button)
        actions_layout.addWidget(self.delete_employee_button)
        actions_layout.addWidget(self.refresh_employee_button)
        actions_layout.addWidget(self.view_employee_button)
        actions_layout.addWidget(self.export_employee_button)
        actions_layout.addWidget(self.statistics_employee_button)

    def create_custom_employees_status_filter(self):
        """إنشاء قائمة تصفية مخصصة للعمال مطابقة للفواتير"""
        # إنشاء إطار للقائمة المنسدلة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 2px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
        """)

        # تخطيط أفقي للإطار
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(2, 2, 2, 2)
        filter_layout.setSpacing(0)

        # إنشاء القائمة المنسدلة
        self.status_filter = QPushButton("جميع الحالات ▼")
        self.status_filter.setStyleSheet("""
            QPushButton {
                background: transparent;
                color: #1f2937;
                border: none;
                padding: 6px 12px;
                font-size: 14px;
                font-weight: 900;
                text-align: left;
                border-radius: 12px;
                min-width: 120px;
                max-height: 30px;
                min-height: 26px;
            }
            QPushButton:hover {
                background: rgba(96, 165, 250, 0.1);
                color: #1e40af;
            }
            QPushButton:pressed {
                background: rgba(96, 165, 250, 0.2);
                color: #1e40af;
            }
        """)

        # إنشاء القائمة المنسدلة
        filter_menu = QMenu(self)
        filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(248, 250, 252, 0.9),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.8);
                border-radius: 12px;
                padding: 8px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #1f2937;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }
            QMenu::item {
                padding: 8px 20px;
                margin: 2px;
                border-radius: 8px;
                background: transparent;
                color: #374151;
                font-weight: 600;
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة خيارات التصفية
        filter_options = [
            ("جميع الحالات", None),
            ("🟢 العمال النشطين", "active"),
            ("🟡 العمال العاديين", "normal"),
            ("🔴 العمال المدينين", "debtor")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_employees_filter(v, t))
            filter_menu.addAction(action)

        self.status_filter.setMenu(filter_menu)

        # إضافة القائمة للإطار
        filter_layout.addWidget(self.status_filter)
        self.status_filter_frame.setLayout(filter_layout)

        # تعيين المتغير للاستخدام في التخطيط
        self.status_filter = self.status_filter_frame

    def set_employees_filter(self, value, text):
        """تعيين تصفية العمال"""
        self.current_filter_value = value
        # تحديث نص الزر
        button = self.status_filter_frame.findChild(QPushButton)
        if button:
            button.setText(f"{text} ▼")
        # تطبيق التصفية
        self.filter_employees()

    def create_custom_wages_type_filter(self):
        """إنشاء قائمة تصفية مخصصة للأجور اليومية"""
        # إنشاء إطار للقائمة المنسدلة
        self.wages_type_filter_frame = QFrame()
        self.wages_type_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 2px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
        """)

        # تخطيط أفقي للإطار
        wages_filter_layout = QHBoxLayout()
        wages_filter_layout.setContentsMargins(2, 2, 2, 2)
        wages_filter_layout.setSpacing(0)

        # إنشاء القائمة المنسدلة
        self.wages_type_filter_button = QPushButton("جميع الأنواع ▼")
        self.wages_type_filter_button.setStyleSheet("""
            QPushButton {
                background: transparent;
                color: #1f2937;
                border: none;
                padding: 6px 12px;
                font-size: 14px;
                font-weight: 900;
                text-align: left;
                border-radius: 12px;
                min-width: 120px;
                max-height: 30px;
                min-height: 26px;
            }
            QPushButton:hover {
                background: rgba(96, 165, 250, 0.1);
                color: #1e40af;
            }
            QPushButton:pressed {
                background: rgba(96, 165, 250, 0.2);
                color: #1e40af;
            }
        """)

        # إنشاء القائمة المنسدلة
        wages_filter_menu = QMenu(self)
        wages_filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(248, 250, 252, 0.9),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.8);
                border-radius: 12px;
                padding: 8px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #1f2937;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }
            QMenu::item {
                padding: 8px 20px;
                margin: 2px;
                border-radius: 8px;
                background: transparent;
                color: #374151;
                font-weight: 600;
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة خيارات التصفية للأجور اليومية
        wages_filter_options = [
            ("جميع الأنواع", None),
            ("💰 أجر يومي عادي", "daily_normal"),
            ("⏰ ساعات إضافية", "overtime"),
            ("🏗️ أجر مشروع", "project_wage"),
            ("🎁 مكافأة أداء", "performance_bonus"),
            ("💼 علاوة", "allowance"),
            ("📋 خصم", "deduction"),
            ("🔧 أجر صيانة", "maintenance"),
            ("🚛 أجر نقل", "transport")
        ]

        for text, value in wages_filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_wages_filter(v, t))
            wages_filter_menu.addAction(action)

        self.wages_type_filter_button.setMenu(wages_filter_menu)

        # إضافة القائمة للإطار
        wages_filter_layout.addWidget(self.wages_type_filter_button)
        self.wages_type_filter_frame.setLayout(wages_filter_layout)

        # تعيين المتغير للاستخدام في التخطيط
        self.wages_type_filter = self.wages_type_filter_frame

    def set_wages_filter(self, value, text):
        """تعيين تصفية الأجور اليومية"""
        self.current_wages_filter_value = value
        # تحديث نص الزر
        button = self.wages_type_filter_frame.findChild(QPushButton)
        if button:
            button.setText(f"{text} ▼")
        # تطبيق التصفية
        self.filter_daily_wages()

    def filter_daily_wages(self):
        """تصفية الأجور اليومية بناءً على نص البحث والنوع"""
        try:
            search_text = self.daily_wages_search_edit.text().strip().lower() if hasattr(self, 'daily_wages_search_edit') else ""
            wage_type = getattr(self, 'current_wages_filter_value', None)

            print(f"🔍 البحث في الأجور اليومية: '{search_text}', النوع: {wage_type}")

            # TODO: هنا سيتم إضافة منطق التصفية الفعلي عند إنشاء جدول الأجور اليومية
            # في الوقت الحالي، نطبع فقط معايير البحث

        except Exception as e:
            print(f"خطأ في تصفية الأجور اليومية: {str(e)}")

    def save_daily_wages_data(self):
        """حفظ بيانات الأجور اليومية - وظيفة مؤقتة"""
        print("💾 حفظ بيانات الأجور اليومية")



    def refresh_data(self):
        """تحديث البيانات - وظيفة مؤقتة"""
        print("🔄 تحديث بيانات العمال")

    # ==================== دوال جدول العمال ====================

    def create_employees_table(self):
        """إنشاء جدول العمال مطابق للموردين مع 9 أعمدة"""
        # إنشاء الجدول
        self.employees_table = QTableWidget()
        self.employees_table.setColumnCount(9)

        # عناوين الأعمدة مع الأيقونات مطابقة للموردين (الترتيب الجديد مع الملاحظات والتاريخ في النهاية)
        headers = [
            "🔢 ID",
            "👷‍♂️ اسم العامل",
            "🏠 العنوان",
            "📧 البريد الإلكتروني",
            "📱 رقم الهاتف",
            "💵 الرصيد",
            "⭐ حالة العامل",
            "📋 الملاحظات",
            "🗓️ تاريخ التوظيف"
        ]

        self.employees_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول مطابقة للموردين
        self.employees_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.employees_table.setSelectionMode(QTableWidget.SingleSelection)
        self.employees_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.employees_table.setAlternatingRowColors(False)
        self.employees_table.setSortingEnabled(True)

        # إعدادات الصفوف والأعمدة مطابقة للموردين
        self.employees_table.verticalHeader().setDefaultSectionSize(50)
        self.employees_table.verticalHeader().setVisible(False)

        header = self.employees_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)

        # إعداد عرض الأعمدة مطابق للموردين (الترتيب الجديد: رقم، اسم، عنوان، بريد، هاتف، رصيد، حالة، ملاحظات، تاريخ)
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # الرقم - عرض ثابت
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # الاسم - عرض قابل للتعديل
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # العنوان - عرض قابل للتعديل
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # البريد - عرض قابل للتعديل
        header.setSectionResizeMode(4, QHeaderView.Interactive)  # الهاتف - عرض ثابت
        header.setSectionResizeMode(5, QHeaderView.Interactive)  # الرصيد - عرض ثابت
        header.setSectionResizeMode(6, QHeaderView.Interactive)  # الحالة - عرض ثابت
        header.setSectionResizeMode(7, QHeaderView.Interactive)  # الملاحظات - عرض قابل للتعديل
        header.setSectionResizeMode(8, QHeaderView.Interactive)  # التاريخ - عرض ثابت

        # تحديد عرض محدد للأعمدة - المقاسات الجديدة المحسنة مطابقة للموردين
        header.resizeSection(0, 100)  # 🔢 ID - 100px
        header.resizeSection(1, 300)  # 👷‍♂️ اسم العامل - 300px
        header.resizeSection(2, 300)  # 🏠 العنوان - 300px
        header.resizeSection(3, 240)  # 📧 البريد الإلكتروني - 240px
        header.resizeSection(4, 170)  # 📱 رقم الهاتف - 170px
        header.resizeSection(5, 160)  # 💵 الرصيد - 160px
        header.resizeSection(6, 170)  # ⭐ حالة العامل - 170px
        header.resizeSection(7, 280)  # 📋 الملاحظات - 280px
        header.resizeSection(8, 180)  # 🗓️ تاريخ التوظيف - 180px

        # تطبيق التصميم والتفاعل مطابق للموردين
        self.apply_employees_table_style()
        self.add_employees_watermark_to_table()
        self.setup_employees_table_interactions()

    def filter_employees(self):
        """تصفية العمال بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower() if hasattr(self, 'search_edit') else ""
            status = getattr(self, 'current_filter_value', None)

            # بناء الاستعلام
            query = self.session.query(Employee)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Employee.name.like(f"%{search_text}%") |
                    Employee.phone.like(f"%{search_text}%") |
                    Employee.email.like(f"%{search_text}%") |
                    Employee.address.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Employee.balance > 0)
            elif status == "normal":
                query = query.filter(Employee.balance == 0)
            elif status == "debtor":
                query = query.filter(Employee.balance < 0)

            # تنفيذ الاستعلام
            employees = query.order_by(Employee.id.asc()).all()

            # تحديث الجدول والملخص
            self.populate_employees_table(employees)
            self.update_employees_summary(employees)

        except Exception as e:
            print(f"حدث خطأ أثناء تصفية العمال: {str(e)}")

    def apply_employees_table_style(self):
        """تطبيق التصميم المتطور لجدول العمال مطابق للموردين"""
        self.employees_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9), stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9), stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9), stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def add_employees_watermark_to_table(self):
        """إضافة علامة مائية لجدول العمال مطابقة للموردين"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))

            # رسم النص الرئيسي بحجم خط كبير جداً (180) في منتصف الارتفاع
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)

            # حساب منتصف الارتفاع
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")

            painter.restore()

        # تطبيق العلامة المائية
        original_paint = self.employees_table.paintEvent
        def new_paint_event(event):
            original_paint(event)
            painter = QPainter(self.employees_table.viewport())
            paint_watermark(painter, self.employees_table.viewport().rect())
            painter.end()

        self.employees_table.paintEvent = new_paint_event

    def setup_employees_table_interactions(self):
        """إعداد التفاعلات مع جدول العمال مطابق للموردين"""
        self.employees_table.itemSelectionChanged.connect(self.on_employees_selection_changed)
        self.employees_table.cellDoubleClicked.connect(self.edit_employee)

        # إلغاء التحديد عند النقر على منطقة فارغة
        def mousePressEvent(event):
            item = self.employees_table.itemAt(event.pos())
            if item is None:
                self.employees_table.clearSelection()
            QTableWidget.mousePressEvent(self.employees_table, event)

        self.employees_table.mousePressEvent = mousePressEvent

    def on_employees_selection_changed(self):
        """معالج تغيير التحديد في جدول العمال مطابق للموردين"""
        selected_items = self.employees_table.selectedItems()
        has_selection = len(selected_items) > 0

        # تفعيل/تعطيل الأزرار حسب التحديد
        selection_dependent_buttons = [
            ('edit_employee_button', "✏️ تعديل"),
            ('delete_employee_button', "🗑️ حذف"),
            ('view_employee_button', "👁️ عرض التفاصيل")
        ]

        for button_attr, button_name in selection_dependent_buttons:
            if hasattr(self, button_attr):
                button = getattr(self, button_attr)
                self.set_button_enabled(button, has_selection, button_name)

        # الأزرار التي تبقى مفعلة دائماً
        always_enabled_buttons = [
            ('add_employee_button', "➕ إضافة عامل"),
            ('refresh_employee_button', "🔄 تحديث"),
            ('export_employee_button', "📤 تصدير"),
            ('statistics_employee_button', "📊 الإحصائيات")
        ]

        for button_attr, button_name in always_enabled_buttons:
            if hasattr(self, button_attr):
                button = getattr(self, button_attr)
                self.set_button_enabled(button, True, button_name)

    def set_button_enabled(self, button, enabled, button_name=""):
        """تفعيل أو تعطيل زر مع التأثيرات البصرية المناسبة"""
        try:
            button.setEnabled(enabled)

            if enabled:
                # إظهار الزر بشفافية كاملة
                print(f"🟢 تفعيل الزر: {button_name}")
                # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                current_style = button.styleSheet()
                # إزالة أي opacity موجودة
                import re
                clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                # إضافة opacity كاملة
                new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                button.setStyleSheet(new_style)
                button.show()
            else:
                # تقليل شفافية الزر (لا نخفيه تماماً)
                print(f"🔴 تعطيل الزر: {button_name}")
                current_style = button.styleSheet()
                # إزالة أي opacity موجودة
                import re
                clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                # إضافة opacity منخفضة
                new_style = clean_style + "\nQPushButton { opacity: 0.3; }"
                button.setStyleSheet(new_style)

        except Exception as e:
            print(f"خطأ في تفعيل/تعطيل الزر {button_name}: {str(e)}")

    def edit_employee(self):
        """تعديل بيانات عامل - دالة مؤقتة"""
        try:
            employee_id, error = self.get_selected_employee_id()
            if error:
                print(f"خطأ: {error}")
                return
            print(f"تعديل العامل رقم: {employee_id}")
            # TODO: إضافة نافذة تعديل العامل
        except Exception as e:
            print(f"خطأ في تعديل العامل: {str(e)}")

    def get_selected_employee_id(self):
        """استخراج معرف العامل المحدد من الجدول مطابق للموردين"""
        try:
            selected_row = self.employees_table.currentRow()
            if selected_row < 0:
                return None, "الرجاء اختيار عامل من القائمة"

            if not self.employees_table.item(selected_row, 0):
                return None, "الرجاء اختيار عامل صالح من القائمة"

            # استخراج الرقم من النص (إزالة # والأيقونات)
            id_text = self.employees_table.item(selected_row, 0).text()
            numbers = re.findall(r'\d+', id_text)
            if not numbers:
                return None, "لا يمكن استخراج رقم العامل"

            employee_id = int(numbers[0])
            return employee_id, None

        except Exception as e:
            return None, f"خطأ في استخراج معرف العامل: {str(e)}"

    def refresh_employees_data(self):
        """تحديث بيانات جدول العمال"""
        try:
            # جلب جميع العمال من قاعدة البيانات (من الأقدم للأحدث)
            employees = self.session.query(Employee).order_by(Employee.id.asc()).all()
            self.populate_employees_table(employees)
            self.update_employees_summary(employees)
        except Exception as e:
            print(f"حدث خطأ أثناء تحديث بيانات العمال: {str(e)}")

    def update_employees_summary(self, employees):
        """تحديث ملخص العمال مطابق للموردين"""
        try:
            # حساب الإحصائيات
            total_employees = len(employees)
            active_employees = len([e for e in employees if (e.balance or 0) > 0])
            debtor_employees = len([e for e in employees if (e.balance or 0) < 0])
            normal_employees = len([e for e in employees if (e.balance or 0) == 0])

            # حساب المبالغ
            total_positive = sum(e.balance for e in employees if (e.balance or 0) > 0)
            total_negative = sum(abs(e.balance) for e in employees if (e.balance or 0) < 0)
            net_balance = sum(e.balance or 0 for e in employees)

            # تحديث النصوص (إذا كان هناك عنصر ملخص)
            if hasattr(self, 'employees_summary_label'):
                summary_text = (f"إجمالي العمال: {total_employees} | "
                              f"نشط: {active_employees} | "
                              f"مدين: {debtor_employees} | "
                              f"عادي: {normal_employees}")
                self.employees_summary_label.setText(summary_text)

            if hasattr(self, 'employees_balance_label'):
                balance_text = (f"الأرصدة الموجبة: {format_currency(total_positive)} | "
                              f"الأرصدة السالبة: {format_currency(total_negative)} | "
                              f"صافي الرصيد: {format_currency(net_balance)}")
                self.employees_balance_label.setText(balance_text)



        except Exception as e:
            print(f"خطأ في تحديث ملخص العمال: {str(e)}")

    def create_employees_sample_data_if_empty(self):
        """إنشاء بيانات تجريبية للعمال إذا كانت قاعدة البيانات فارغة"""
        try:
            # التحقق من وجود عمال
            employee_count = self.session.query(Employee).count()

            if employee_count == 0:
                # إنشاء عمال تجريبيين مع الحقول الجديدة
                import datetime
                sample_employees = [
                    Employee(
                        name="أحمد محمد علي",
                        address="القاهرة، شبرا الخيمة",
                        phone="01234567890",
                        email="<EMAIL>",
                        position="مهندس مدني",
                        salary=8000.0,
                        balance=2000.0,
                        notes="عامل متميز - أداء ممتاز",
                        hire_date=datetime.datetime.now() - datetime.timedelta(days=365)
                    ),
                    Employee(
                        name="فاطمة أحمد حسن",
                        address="الجيزة، الدقي",
                        phone="01098765432",
                        email="<EMAIL>",
                        position="محاسبة",
                        salary=6500.0,
                        balance=-500.0,
                        notes="تحتاج تسوية مالية",
                        hire_date=datetime.datetime.now() - datetime.timedelta(days=300)
                    ),
                    Employee(
                        name="محمد عبد الله سالم",
                        address="الإسكندرية، المنتزه",
                        phone="01555123456",
                        email="<EMAIL>",
                        position="فني كهرباء",
                        salary=5500.0,
                        balance=0.0,
                        notes="عامل جديد في فترة التدريب",
                        hire_date=datetime.datetime.now() - datetime.timedelta(days=180)
                    ),
                    Employee(
                        name="سارة محمود إبراهيم",
                        address="أسوان، وسط البلد",
                        phone="01777888999",
                        email="<EMAIL>",
                        position="مديرة مشاريع",
                        salary=12000.0,
                        balance=5000.0,
                        notes="قائدة فريق - مسؤولة عن المشاريع الكبرى",
                        hire_date=datetime.datetime.now() - datetime.timedelta(days=500)
                    ),
                    Employee(
                        name="خالد عبد الرحمن طه",
                        address="الغردقة، السقالة",
                        phone="01666555444",
                        email="<EMAIL>",
                        position="عامل بناء",
                        salary=4000.0,
                        balance=-1000.0,
                        notes="يحتاج متابعة للحضور والانصراف",
                        hire_date=datetime.datetime.now() - datetime.timedelta(days=120)
                    )
                ]

                # إضافة العمال لقاعدة البيانات
                for employee in sample_employees:
                    self.session.add(employee)

                # حفظ التغييرات
                self.session.commit()

        except Exception as e:
            # التراجع عن التغييرات في حالة الخطأ
            self.session.rollback()
            print(f"خطأ في إنشاء البيانات التجريبية للعمال: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#166e5b', 'hover_mid': '#148877', 'hover_end': '#167f66', 'hover_bottom': '#20c991',
                    'hover_border': '#10b981', 'pressed_start': '#042e2b', 'pressed_mid': '#035847',
                    'pressed_end': '#044f36', 'pressed_bottom': '#009971', 'pressed_border': '#059669',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#0369a1', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#1c5a7e', 'hover_mid': '#1379b1', 'hover_end': '#1294d7', 'hover_bottom': '#1eb5f9',
                    'hover_border': '#0ea5e9', 'pressed_start': '#023a5e', 'pressed_mid': '#025991',
                    'pressed_end': '#0274b7', 'pressed_bottom': '#0e95d9', 'pressed_border': '#0284c7',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#8f2d2d', 'hover_mid': '#a92b2b', 'hover_end': '#c92c2c', 'hover_bottom': '#ec3636',
                    'hover_border': '#dc2626', 'pressed_start': '#6f0d0d', 'pressed_mid': '#890b0b',
                    'pressed_end': '#a90c0c', 'pressed_bottom': '#cc1616', 'pressed_border': '#b91c1c',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#134e4a', 'bg_mid': '#0f766e', 'bg_end': '#0d9488', 'bg_bottom': '#14b8a6',
                    'hover_start': '#235e5a', 'hover_mid': '#1f867e', 'hover_end': '#1da498', 'hover_bottom': '#24c8b6',
                    'hover_border': '#14b8a6', 'pressed_start': '#033e3a', 'pressed_mid': '#0f665e',
                    'pressed_end': '#0d8478', 'pressed_bottom': '#14a896', 'pressed_border': '#0d9488',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'indigo': {
                    'bg_start': '#312e81', 'bg_mid': '#3730a3', 'bg_end': '#4338ca', 'bg_bottom': '#4f46e5',
                    'hover_start': '#413e91', 'hover_mid': '#4740b3', 'hover_end': '#5348da', 'hover_bottom': '#5f56f5',
                    'hover_border': '#4f46e5', 'pressed_start': '#211e71', 'pressed_mid': '#272093',
                    'pressed_end': '#3328ba', 'pressed_bottom': '#3f36d5', 'pressed_border': '#4338ca',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'orange': {
                    'bg_start': '#9a3412', 'bg_mid': '#c2410c', 'bg_end': '#ea580c', 'bg_bottom': '#f97316',
                    'hover_start': '#aa4422', 'hover_mid': '#d2511c', 'hover_end': '#fa681c', 'hover_bottom': '#ff8326',
                    'hover_border': '#f97316', 'pressed_start': '#8a2402', 'pressed_mid': '#b2310c',
                    'pressed_end': '#da480c', 'pressed_bottom': '#e96306', 'pressed_border': '#ea580c',
                    'border': '#f97316', 'text': '#ffffff', 'shadow': 'rgba(249, 115, 22, 0.5)'
                },
                'rose': {
                    'bg_start': '#881337', 'bg_mid': '#be185d', 'bg_end': '#e11d48', 'bg_bottom': '#f43f5e',
                    'hover_start': '#982347', 'hover_mid': '#ce286d', 'hover_end': '#f12d58', 'hover_bottom': '#ff4f6e',
                    'hover_border': '#f43f5e', 'pressed_start': '#780327', 'pressed_mid': '#ae084d',
                    'pressed_end': '#d10d38', 'pressed_bottom': '#e42f4e', 'pressed_border': '#e11d48',
                    'border': '#f43f5e', 'text': '#ffffff', 'shadow': 'rgba(244, 63, 94, 0.5)'
                }
            }

            # الحصول على ألوان النوع المحدد
            color_set = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور
            style = f"""
                QPushButton {{
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    font-size: 14px;
                    font-weight: 700;
                    color: {color_set['text']};
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['bg_start']},
                        stop:0.3 {color_set['bg_mid']},
                        stop:0.7 {color_set['bg_end']},
                        stop:1 {color_set['bg_bottom']});
                    border: 5px solid {color_set['border']};
                    border-radius: 20px;
                    padding: 8px 16px;
                    min-height: 34px;
                    max-height: 38px;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               2px 2px 4px rgba(0, 0, 0, 0.7),
                               1px 1px 2px rgba(0, 0, 0, 0.5);
                    box-shadow: 0 8px 20px {color_set['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_set['shadow']};
                    margin: 2px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['hover_start']},
                        stop:0.3 {color_set['hover_mid']},
                        stop:0.7 {color_set['hover_end']},
                        stop:1 {color_set['hover_bottom']});
                    border: 5px solid {color_set['hover_border']};
                    box-shadow: 0 12px 25px {color_set['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.5),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.5),
                               0 0 40px {color_set['shadow']};
                    transform: translateY(-2px);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['pressed_start']},
                        stop:0.3 {color_set['pressed_mid']},
                        stop:0.7 {color_set['pressed_end']},
                        stop:1 {color_set['pressed_bottom']});
                    border: 5px solid {color_set['pressed_border']};
                    box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.6),
                               inset 0 -1px 0 rgba(255, 255, 255, 0.2),
                               0 2px 5px {color_set['shadow']};
                    transform: translateY(1px);
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #6b7280,
                        stop:0.3 #9ca3af,
                        stop:0.7 #d1d5db,
                        stop:1 #e5e7eb);
                    border: 5px solid #9ca3af;
                    color: #374151;
                    box-shadow: none;
                    text-shadow: none;
                    transform: none;
                }}
            """

            # إضافة تأثير القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            if has_menu:
                style += """
                    QPushButton::menu-indicator {
                        image: none;
                        width: 0px;
                    }
                """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")
            # تطبيق تصميم بسيط في حالة الخطأ
            button.setStyleSheet("""
                QPushButton {
                    background-color: #3b82f6;
                    color: white;
                    border: 2px solid #1d4ed8;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2563eb;
                }
                QPushButton:pressed {
                    background-color: #1d4ed8;
                }
            """)

    def initialize_employees_button_states(self):
        """تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة"""
        try:
            print("🔧 بدء تهيئة حالة أزرار العمال...")

            # تفعيل جميع الأزرار وجعلها منيرة
            buttons = [
                (self.add_employee_button, "➕ إضافة عامل"),
                (self.edit_employee_button, "✏️ تعديل"),
                (self.delete_employee_button, "🗑️ حذف"),
                (self.refresh_employee_button, "🔄 تحديث"),
                (self.view_employee_button, "👁️ عرض التفاصيل"),
                (self.export_employee_button, "📤 تصدير"),
                (self.statistics_employee_button, "📊 الإحصائيات")
            ]

            for button, name in buttons:
                if button:
                    button.setEnabled(True)
                    # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                    current_style = button.styleSheet()
                    # إزالة أي opacity موجودة
                    import re
                    clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                    # إضافة opacity كاملة
                    new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                    button.setStyleSheet(new_style)
                    button.show()
                    print(f"🟢 تم تفعيل الزر: {name}")

            print("✅ تم تهيئة حالة أزرار العمال بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تهيئة حالة أزرار العمال: {str(e)}")

    def add_employee(self):
        """إضافة عامل جديد"""
        try:
            print("➕ إضافة عامل جديد...")
            # TODO: إضافة نافذة حوار لإضافة عامل جديد
            print("🔧 سيتم تطوير نافذة إضافة العامل قريباً")
        except Exception as e:
            print(f"خطأ في إضافة العامل: {str(e)}")

    def delete_employee(self):
        """حذف العامل المحدد"""
        try:
            employee_id, error = self.get_selected_employee_id()
            if error:
                print(f"خطأ: {error}")
                return
            print(f"🗑️ حذف العامل رقم: {employee_id}")
            # TODO: إضافة تأكيد الحذف وحذف العامل من قاعدة البيانات
            print("🔧 سيتم تطوير وظيفة حذف العامل قريباً")
        except Exception as e:
            print(f"خطأ في حذف العامل: {str(e)}")

    def view_employee_details(self):
        """عرض تفاصيل العامل المحدد"""
        try:
            employee_id, error = self.get_selected_employee_id()
            if error:
                print(f"خطأ: {error}")
                return
            print(f"👁️ عرض تفاصيل العامل رقم: {employee_id}")
            # TODO: إضافة نافذة عرض تفاصيل العامل
            print("🔧 سيتم تطوير نافذة عرض التفاصيل قريباً")
        except Exception as e:
            print(f"خطأ في عرض تفاصيل العامل: {str(e)}")

    def export_employees_to_excel(self):
        """تصدير بيانات العمال إلى Excel"""
        try:
            print("📊 تصدير بيانات العمال إلى Excel...")
            # TODO: تطوير وظيفة التصدير إلى Excel
            print("🔧 سيتم تطوير وظيفة التصدير إلى Excel قريباً")
        except Exception as e:
            print(f"خطأ في تصدير البيانات إلى Excel: {str(e)}")

    def export_employees_to_csv(self):
        """تصدير بيانات العمال إلى CSV"""
        try:
            print("📄 تصدير بيانات العمال إلى CSV...")
            # TODO: تطوير وظيفة التصدير إلى CSV
            print("🔧 سيتم تطوير وظيفة التصدير إلى CSV قريباً")
        except Exception as e:
            print(f"خطأ في تصدير البيانات إلى CSV: {str(e)}")

    def show_employees_statistics(self):
        """عرض إحصائيات العمال"""
        try:
            print("📊 عرض إحصائيات العمال...")
            # TODO: تطوير نافذة الإحصائيات
            print("🔧 سيتم تطوير نافذة الإحصائيات قريباً")
        except Exception as e:
            print(f"خطأ في عرض الإحصائيات: {str(e)}")

    def populate_employees_table(self, employees):
        """ملء جدول العمال بالبيانات مطابق للموردين"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.employees_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.employees_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن مطابق للموردين
            for row, employee in enumerate(employees):
                try:
                    self.employees_table.insertRow(row)

                    # 1. الرقم التسلسلي مع تنسيق متطور وأيقونة مطابق للموردين
                    id_item = QTableWidgetItem(f"🔢 #{str(employee.id).zfill(4)}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setFont(QFont("Consolas", 12, QFont.Bold))
                    id_item.setForeground(QColor("#0f172a"))
                    id_item.setToolTip(f"🔢 الرقم التسلسلي: {employee.id}")
                    self.employees_table.setItem(row, 0, id_item)

                    # 2. اسم العامل مع تنسيق احترافي
                    name_text = employee.name if employee.name and employee.name.strip() else "No data"
                    name_item = QTableWidgetItem(f"👷‍♂️ {name_text}")
                    name_item.setTextAlignment(Qt.AlignCenter)
                    name_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    if name_text == "No data":
                        name_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        name_item.setForeground(QColor("#1e40af"))
                    name_item.setToolTip(f"👷‍♂️ اسم العامل: {name_text}\n💡 انقر نقرتين للتعديل")
                    self.employees_table.setItem(row, 1, name_item)

                    # 3. العنوان مع تنسيق جذاب
                    address_text = employee.address if employee.address and employee.address.strip() else "No data"
                    address_item = QTableWidgetItem(f"🏠 {address_text}")
                    address_item.setTextAlignment(Qt.AlignCenter)
                    address_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    if address_text == "No data":
                        address_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        address_item.setForeground(QColor("#065f46"))
                    address_item.setToolTip(f"🏠 العنوان: {address_text}")
                    self.employees_table.setItem(row, 2, address_item)

                    # 4. البريد الإلكتروني مع تنسيق أنيق
                    email_text = employee.email if employee.email and employee.email.strip() else "No data"
                    email_item = QTableWidgetItem(f"📧 {email_text}")
                    email_item.setTextAlignment(Qt.AlignCenter)
                    email_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    if email_text == "No data":
                        email_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        email_item.setForeground(QColor("#374151"))
                    email_item.setToolTip(f"📧 البريد الإلكتروني: {email_text}")
                    self.employees_table.setItem(row, 3, email_item)

                    # 5. رقم الهاتف مع تنسيق متطور
                    phone_text = employee.phone if employee.phone and employee.phone.strip() else "No data"
                    phone_item = QTableWidgetItem(f"📱 {phone_text}")
                    phone_item.setTextAlignment(Qt.AlignCenter)
                    phone_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    if phone_text == "No data":
                        phone_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        phone_item.setForeground(QColor("#374151"))
                    phone_item.setToolTip(f"📱 رقم الهاتف: {phone_text}")
                    self.employees_table.setItem(row, 4, phone_item)

                    # 6. الرصيد مع تنسيق مالي متطور مطابق للموردين
                    try:
                        balance = employee.balance or 0
                        if balance and balance != 0:
                            balance_formatted = f"{int(abs(balance)):,}".replace(',', '٬')
                            if balance > 0:
                                balance_display = f"💎 {balance_formatted} جنيه"
                                balance_color = QColor("#059669")  # أخضر للموجب
                            else:
                                balance_display = f"💸 {balance_formatted} جنيه"
                                balance_color = QColor("#dc2626")  # أحمر للسالب
                        else:
                            balance_display = "💵 0 جنيه"
                            balance_color = QColor("#6b7280")  # رمادي للصفر
                    except Exception:
                        balance_display = "💵 0 جنيه"
                        balance_color = QColor("#6b7280")

                    balance_item = QTableWidgetItem(balance_display)
                    balance_item.setTextAlignment(Qt.AlignCenter)
                    balance_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    balance_item.setForeground(balance_color)
                    balance_item.setToolTip(f"💵 رصيد العامل: {balance_display}")
                    self.employees_table.setItem(row, 5, balance_item)

                    # 7. حالة العامل مع تصميم متطور ومؤشرات بصرية مطابق للموردين
                    status_map = {
                        'active': {
                            'text': '🎉 نشط',
                            'color': QColor("#059669"),
                            'tooltip': '🎉 العامل نشط ولديه رصيد موجب\n✅ يمكن التعامل معه'
                        },
                        'normal': {
                            'text': '🎯 عادي',
                            'color': QColor("#d97706"),
                            'tooltip': '🎯 العامل برصيد صفر\n💡 حالة عادية'
                        },
                        'debtor': {
                            'text': '⚠️ مدين',
                            'color': QColor("#dc2626"),
                            'tooltip': '⚠️ العامل مدين برصيد سالب\n🔄 يتطلب متابعة'
                        }
                    }

                    # تحديد حالة العامل
                    if balance > 0:
                        status_key = 'active'
                    elif balance == 0:
                        status_key = 'normal'
                    else:
                        status_key = 'debtor'

                    status_info = status_map.get(status_key, {
                        'text': f"❓ غير محدد",
                        'color': QColor("#6b7280"),
                        'tooltip': f"❓ حالة غير معروفة"
                    })

                    status_item = QTableWidgetItem(status_info['text'])
                    status_item.setTextAlignment(Qt.AlignCenter)
                    status_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                    status_item.setForeground(status_info['color'])
                    status_item.setToolTip(status_info['tooltip'])
                    self.employees_table.setItem(row, 6, status_item)

                    # 8. الملاحظات مع تنسيق أنيق
                    notes_text = employee.notes if employee.notes and employee.notes.strip() else "No data"
                    notes_item = QTableWidgetItem(f"📋 {notes_text}")
                    notes_item.setTextAlignment(Qt.AlignCenter)
                    notes_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    if notes_text == "No data":
                        notes_item.setForeground(QColor("#ef4444"))  # أحمر
                    else:
                        notes_item.setForeground(QColor("#374151"))
                    notes_item.setToolTip(f"📋 الملاحظات: {notes_text}")
                    self.employees_table.setItem(row, 7, notes_item)

                    # 9. تاريخ التوظيف مع تنسيق التاريخ
                    try:
                        if hasattr(employee, 'hire_date') and employee.hire_date:
                            date_formatted = employee.hire_date.strftime("%d/%m/%Y")
                            date_display = f"🗓️ {date_formatted}"
                        else:
                            date_display = "🗓️ غير محدد"
                    except Exception:
                        date_display = "🗓️ غير محدد"

                    date_item = QTableWidgetItem(date_display)
                    date_item.setTextAlignment(Qt.AlignCenter)
                    date_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                    date_item.setForeground(QColor("#374151"))
                    date_item.setToolTip(f"🗓️ تاريخ توظيف العامل: {date_display}")
                    self.employees_table.setItem(row, 8, date_item)

                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية
                    print(f"خطأ في الصف {row}: {str(row_error)}")
                    continue

            # إعادة تفعيل تحديث الجدول
            self.employees_table.setUpdatesEnabled(True)

        except Exception as e:
            # إعادة تفعيل تحديث الجدول في حالة الخطأ
            self.employees_table.setUpdatesEnabled(True)
            print(f"حدث خطأ أثناء تحديث جدول العمال: {str(e)}")
